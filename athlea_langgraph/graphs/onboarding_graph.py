"""
Onboarding Graph

This module creates the enhanced onboarding graph that guides users through
the process of gathering their fitness information, generating a personalized plan,
and creating intelligent training sessions.
"""

import logging
from typing import Any, Dict, Literal, Optional

from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import END, START, StateGraph

from athlea_langgraph.agents.onboarding import (
    check_completion_node,
    generate_plan_node,
    generate_plan_summary_node,
    information_gatherer_node,
)

# Import new session generation nodes
from athlea_langgraph.agents.session_generation_nodes import (
    session_generation_node,
    coach_coordination_node,
    user_control_node,
    session_adaptation_node,
    session_flow_router,
)

from athlea_langgraph.states.onboarding_state import (
    OnboardingState,
    create_initial_onboarding_state,
)
from athlea_langgraph.services.mem0_memory_service import get_mem0_service

logger = logging.getLogger(__name__)


async def plan_review_node(state: OnboardingState) -> dict:
    """
    This node creates a declarative interruption point and ensures the generated plan
    is properly formatted for the frontend when the graph interrupts.
    The graph's `compile` method is configured to interrupt *after* this node runs,
    allowing the user to review the generated plan.
    """
    logger.info(
        "[Node: plan_review_node] Plan generated. Preparing plan data for frontend and pausing for user review and approval."
    )

    # Get the current state data
    generated_plan = state.get("generated_plan")
    sidebar_data = state.get("sidebar_data")
    onboarding_stage = state.get("onboarding_stage", "complete")

    # Create a message to show the plan to the user
    plan_message = None
    if generated_plan:
        plan_name = generated_plan.get("name", "Your Training Plan")
        plan_description = generated_plan.get("description", "")

        # Create a comprehensive plan presentation message
        plan_content = f"""🎯 **{plan_name}**

{plan_description}

**Duration:** {generated_plan.get('duration', 'Not specified')}
**Level:** {generated_plan.get('level', 'Not specified')}
**Focus Areas:** {', '.join(generated_plan.get('disciplines', []))}

{generated_plan.get('rationale', '')}

Your complete training plan with detailed phases and sessions is now available in the sidebar. Review the plan and let me know if you'd like any adjustments!"""

        from langchain_core.messages import AIMessage

        plan_message = AIMessage(content=plan_content)

        logger.info(
            f"[Node: plan_review_node] Created plan presentation message for plan: {plan_name}"
        )

    # Return the plan data to ensure it's available when the graph interrupts
    result = {
        "onboarding_stage": onboarding_stage,
    }

    # Include the generated plan and sidebar data
    if generated_plan:
        result["generated_plan"] = generated_plan
    if sidebar_data:
        result["sidebar_data"] = sidebar_data
    if plan_message:
        result["messages"] = [plan_message]

    logger.info(
        f"[Node: plan_review_node] Returning plan data for frontend: stage={onboarding_stage}, has_plan={bool(generated_plan)}"
    )

    return result


class OnboardingGraph:
    """Enhanced onboarding graph with adaptive weekly session generation using multi-agent coordination"""

    def __init__(
        self,
        checkpointer=None,
        enable_mem0: bool = True,
        mem0_use_api: bool = False,
    ):
        """Initialize the onboarding graph with adaptive weekly planning capabilities

        Args:
            checkpointer: Optional checkpointer for state persistence
            enable_mem0: Whether to enable Mem0 memory integration
            mem0_use_api: Whether to use Mem0 API (True) or local instance (False)
        """
        self.checkpointer = checkpointer or MemorySaver()
        self.enable_mem0 = enable_mem0
        self.mem0_use_api = mem0_use_api
        self._graph = None
        self._mem0_service = None

    async def _get_mem0_service(self):
        """Get the Mem0 service instance (lazy initialization)"""
        if self.enable_mem0 and self._mem0_service is None:
            try:
                self._mem0_service = await get_mem0_service(use_api=self.mem0_use_api)
                logger.info("✅ Mem0 service initialized for onboarding")
            except Exception as e:
                logger.error(f"❌ Failed to initialize Mem0 service: {e}")
                self._mem0_service = None
        return self._mem0_service

    def _after_gather_info_router(
        self, state: OnboardingState
    ) -> Literal["checkCompletion", "END"]:
        """
        Decides whether to end the turn after a greeting or continue to check for completion.
        Now uses full conversation history from MongoDB for proper routing decisions.
        Enhanced to handle users returning after plan summary.
        """
        # Check if user is returning after plan summary was sent
        plan_summary_sent = state.get("plan_summary_sent", False)
        onboarding_stage = state.get("onboarding_stage", "")

        logger.info(
            f"[Graph Router] _after_gather_info_router - plan_summary_sent: {plan_summary_sent}, onboarding_stage: {onboarding_stage}"
        )

        # If user was at plan summary stage, route to checkCompletion
        # which will then route to generatePlanSummary for confirmation handling
        if plan_summary_sent or onboarding_stage == "plan_summary_ready":
            logger.info(
                "[Graph Router] 🎯 User at plan summary stage - routing to checkCompletion"
            )
            return "checkCompletion"

        # Get conversation history from state (loaded from MongoDB)
        conversation_history = state.get("conversation_history", [])
        local_messages = state.get("messages", [])

        # Count total messages from both sources
        total_message_count = len(conversation_history) + len(local_messages)

        logger.info(
            f"[Graph Router] Message count analysis: "
            f"conversation_history: {len(conversation_history)}, "
            f"local_messages: {len(local_messages)}, "
            f"total: {total_message_count}"
        )

        # Check if the information gatherer determined we have enough info
        has_enough_info = state.get("has_enough_info", False)

        logger.info(f"[Graph Router] has_enough_info: {has_enough_info}")

        # If we have enough info, proceed to completion check
        if has_enough_info:
            logger.info(
                "[Graph Router] ✅ Has enough info - routing to checkCompletion"
            )
            return "checkCompletion"
        else:
            # Otherwise, end to await more user input
            logger.info(
                f"[Graph Router] ✅ ENDING after information gathering to await user response."
            )
            return "END"

    async def enhanced_information_gathering_node(
        self, state: OnboardingState
    ) -> Dict[str, Any]:
        """Enhanced information gathering with memory context injection"""
        logger.info(
            "🧠 [ONBOARDING] Enhanced information gathering with memory context"
        )

        # CRITICAL: Reset completion flags to prevent infinite loops
        # These flags should only persist for one routing decision
        logger.info("🔄 [ONBOARDING] Resetting completion flags to prevent loops")
        state["has_enough_info"] = False
        state["plan_summary_sent"] = False
        state["onboarding_stage"] = (
            "gathering"  # Reset to gathering stage when user is clearly still gathering info
        )

        # CRITICAL: Also update sidebar data current_stage to sync with frontend
        if "sidebar_data" in state and state["sidebar_data"]:
            sidebar_data = state["sidebar_data"]
            if hasattr(sidebar_data, "model_dump"):
                # Pydantic object - need to update it properly
                sidebar_dict = sidebar_data.model_dump()
                sidebar_dict["current_stage"] = "gathering"
                from athlea_langgraph.states.onboarding_state import SidebarStateData

                state["sidebar_data"] = SidebarStateData(**sidebar_dict)
                logger.info(
                    "🔄 [ONBOARDING] Updated Pydantic sidebar current_stage to 'gathering'"
                )
            elif isinstance(sidebar_data, dict):
                # Dict object - update directly
                sidebar_data["current_stage"] = "gathering"
                logger.info(
                    "🔄 [ONBOARDING] Updated dict sidebar current_stage to 'gathering'"
                )

        # Get memory context before processing
        user_id = state.get("user_id")
        if user_id and self.enable_mem0:
            try:
                mem0_service = await self._get_mem0_service()
                if mem0_service:
                    # Retrieve onboarding context
                    context = await mem0_service.get_user_coaching_context(
                        user_id=user_id,
                        query="onboarding fitness goals preferences history",
                        limit=5,
                    )

                    if context:
                        # Inject memory context into state
                        logger.info(
                            f"🧠 Retrieved {len(context)} memory items for onboarding context"
                        )
                        # Handle both dict and string memory formats
                        memory_texts = []
                        for item in context:
                            if isinstance(item, dict):
                                memory_text = item.get("memory", str(item))
                            else:
                                memory_text = str(item)
                            memory_texts.append(f"- {memory_text}")

                        state["memory_context"] = "\n".join(memory_texts)
                    else:
                        logger.info("🔍 No previous onboarding context found")
            except Exception as e:
                logger.error(f"❌ Error retrieving memory context: {e}")

        # Call original information gatherer node
        from athlea_langgraph.agents.onboarding import information_gatherer_node

        logger.info("🧠 [ONBOARDING] Calling information_gatherer_node...")
        result = await information_gatherer_node(state)
        logger.info(
            f"🧠 [ONBOARDING] information_gatherer_node completed! Result keys: {list(result.keys()) if result else 'None'}"
        )

        return result

    async def enhanced_plan_generation_node(
        self, state: OnboardingState
    ) -> Dict[str, Any]:
        """Enhanced plan generation with memory storage"""
        logger.info("🧠 [ONBOARDING] Enhanced plan generation with memory storage")

        # Call original plan generation node
        from athlea_langgraph.agents.onboarding import generate_plan_node

        result = await generate_plan_node(state)

        # Store onboarding completion in memory
        user_id = state.get("user_id")
        if user_id and self.enable_mem0 and result.get("generated_plan"):
            try:
                mem0_service = await self._get_mem0_service()
                if mem0_service:
                    plan = result["generated_plan"]
                    sidebar_data = result.get("sidebar_data")

                    # Handle both Pydantic objects and dict formats
                    if isinstance(plan, dict):
                        plan_name = plan.get("name", "")
                        plan_type = plan.get("planType", "")
                        level = plan.get("level", "")
                        duration = plan.get("duration", "")
                        disciplines = plan.get("disciplines", [])
                        rationale = plan.get("rationale", "")
                        plan_id = plan.get("planId", "")
                    else:
                        # Assume it's a Pydantic object
                        plan_name = plan.name
                        plan_type = plan.plan_type
                        level = plan.level
                        duration = plan.duration
                        disciplines = plan.disciplines
                        rationale = plan.rationale
                        plan_id = plan.plan_id

                    # Handle sidebar_data safely
                    selected_sports = "None"
                    goals = "None"
                    if sidebar_data:
                        if isinstance(sidebar_data, dict):
                            selected_sports = ", ".join(
                                sidebar_data.get("selected_sports", [])
                            )
                            goals_data = sidebar_data.get("goals", {})
                            if isinstance(goals_data, dict):
                                goals = ", ".join(goals_data.get("list", []))
                        else:
                            # Assume it's a Pydantic object
                            selected_sports = (
                                ", ".join(sidebar_data.selected_sports)
                                if sidebar_data.selected_sports
                                else "None"
                            )
                            goals = (
                                ", ".join(sidebar_data.goals.list)
                                if sidebar_data.goals and sidebar_data.goals.list
                                else "None"
                            )

                    # Create comprehensive memory of onboarding completion
                    memory_text = f"""
                    User completed onboarding process:
                    - Generated Plan: {plan_name}
                    - Plan Type: {plan_type}
                    - Level: {level}
                    - Duration: {duration}
                    - Disciplines: {', '.join(disciplines)}
                    - Selected Sports: {selected_sports}
                    - Goals: {goals}
                    - Plan Rationale: {rationale}
                    """

                    await mem0_service.add_coaching_memory(
                        content=memory_text,
                        user_id=user_id,
                        metadata={
                            "type": "onboarding_completion",
                            "plan_id": plan_id,
                            "plan_name": plan_name,
                            "disciplines": disciplines,
                            "level": level,
                        },
                    )

                    logger.info("✅ Stored onboarding completion in Mem0")
            except Exception as e:
                logger.error(f"❌ Error storing onboarding completion: {e}")

        return result

    async def standard_information_gathering_node(
        self, state: OnboardingState
    ) -> Dict[str, Any]:
        """Standard information gathering with completion flag reset"""
        logger.info("📝 [ONBOARDING] Standard information gathering")

        # CRITICAL: Reset completion flags to prevent infinite loops
        # These flags should only persist for one routing decision
        logger.info("🔄 [ONBOARDING] Resetting completion flags to prevent loops")
        state["has_enough_info"] = False
        state["plan_summary_sent"] = False
        state["onboarding_stage"] = (
            "gathering"  # Reset to gathering stage when user is clearly still gathering info
        )

        # CRITICAL: Also update sidebar data current_stage to sync with frontend
        if "sidebar_data" in state and state["sidebar_data"]:
            sidebar_data = state["sidebar_data"]
            if hasattr(sidebar_data, "model_dump"):
                # Pydantic object - need to update it properly
                sidebar_dict = sidebar_data.model_dump()
                sidebar_dict["current_stage"] = "gathering"
                from athlea_langgraph.states.onboarding_state import SidebarStateData

                state["sidebar_data"] = SidebarStateData(**sidebar_dict)
                logger.info(
                    "🔄 [ONBOARDING] Updated Pydantic sidebar current_stage to 'gathering'"
                )
            elif isinstance(sidebar_data, dict):
                # Dict object - update directly
                sidebar_data["current_stage"] = "gathering"
                logger.info(
                    "🔄 [ONBOARDING] Updated dict sidebar current_stage to 'gathering'"
                )

        # Call original information gatherer node
        from athlea_langgraph.agents.onboarding import information_gatherer_node

        logger.info("📝 [ONBOARDING] Calling information_gatherer_node...")
        result = await information_gatherer_node(state)
        logger.info(
            f"📝 [ONBOARDING] information_gatherer_node completed! Result keys: {list(result.keys()) if result else 'None'}"
        )

        return result

    def create_graph(self) -> StateGraph:
        """Create and configure the simplified onboarding graph following LangGraph best practices"""
        logger.info(
            "[OnboardingGraph] Creating simplified onboarding graph with clear linear flow"
        )

        # Create the state graph
        graph = StateGraph(OnboardingState)

        # Add onboarding nodes (enhanced if Mem0 enabled)
        if self.enable_mem0:
            graph.add_node("gatherInfo", self.enhanced_information_gathering_node)
            graph.add_node("generatePlan", self.enhanced_plan_generation_node)
            logger.info("🧠 Added enhanced nodes with Mem0 integration")
        else:
            graph.add_node("gatherInfo", self.standard_information_gathering_node)
            graph.add_node("generatePlan", generate_plan_node)
            logger.info("📝 Added standard nodes with completion flag reset")

        # Add completion checker and plan summary
        graph.add_node("checkCompletion", check_completion_node)
        graph.add_node("generatePlanSummary", generate_plan_summary_node)

        # Add plan review node
        graph.add_node("plan_review", plan_review_node)

        # Add session generation nodes
        graph.add_node("sessionGeneration", session_generation_node)
        graph.add_node("coachCoordination", coach_coordination_node)
        graph.add_node("userControl", user_control_node)
        graph.add_node("sessionAdaptation", session_adaptation_node)

        # SIMPLIFIED LINEAR FLOW - Following LangGraph Best Practices
        graph.add_edge(START, "gatherInfo")

        # After gathering info, always check completion (no conditional branching here)
        graph.add_conditional_edges(
            "gatherInfo",
            self._after_gather_info_router,
            {"checkCompletion": "checkCompletion", "END": END},
        )

        # After checking completion, either generate plan summary or end
        graph.add_conditional_edges(
            "checkCompletion",
            self._simplified_completion_router,
            {"generatePlanSummary": "generatePlanSummary", "END": END},
        )

        # After plan summary, user confirms and we generate the full plan
        graph.add_conditional_edges(
            "generatePlanSummary",
            self._plan_summary_confirmation_router,
            {"generatePlan": "generatePlan", "gatherInfo": "gatherInfo", "END": END},
        )

        # After plan generation in onboarding, use the onboarding-specific feedback router
        graph.add_conditional_edges(
            "generatePlan",
            self._onboarding_plan_feedback_router,
            {
                "generatePlan": "generatePlan",  # Regenerate with feedback
                "END": END,  # Plan approved, end onboarding
            },
        )

        # Keep plan_review for session generation flow only
        # After review, handle approval/rejection
        graph.add_conditional_edges(
            "plan_review",
            self._onboarding_plan_feedback_router,
            {
                "sessionGeneration": "sessionGeneration",
                "generatePlan": "generatePlan",  # Regenerate only
                "END": END,
            },
        )

        # Session generation flow
        graph.add_edge("sessionGeneration", "coachCoordination")
        graph.add_edge("coachCoordination", "userControl")

        # Conditional routing from userControl
        graph.add_conditional_edges(
            "userControl",
            session_flow_router,
            {
                "user_control": "userControl",
                "session_adaptation": "sessionAdaptation",
                "complete": END,
            },
        )

        # Session adaptation
        graph.add_conditional_edges(
            "sessionAdaptation",
            session_flow_router,
            {
                "user_control": "userControl",
                "session_adaptation": "sessionAdaptation",
                "complete": END,
            },
        )

        logger.info(
            "[OnboardingGraph] Simplified graph created successfully with linear flow"
        )
        return graph

    def _simplified_completion_router(
        self, state: OnboardingState
    ) -> Literal["generatePlanSummary", "END"]:
        """Simplified router that checks if we have enough info to generate a plan summary"""
        plan_summary_sent = state.get("plan_summary_sent", False)
        onboarding_stage = state.get("onboarding_stage", "")
        has_enough_info = state.get("has_enough_info", False)

        logger.info(
            f"[Graph Router] Completion check - plan_summary_sent: {plan_summary_sent}, "
            f"onboarding_stage: {onboarding_stage}, has_enough_info: {has_enough_info}"
        )

        # If user was already at plan summary stage, route to generatePlanSummary
        # so the plan_summary_confirmation_router can handle the user's response
        if plan_summary_sent or onboarding_stage == "plan_summary_ready":
            logger.info(
                "[Graph Router] 🎯 User already has plan summary - routing to generatePlanSummary for confirmation handling"
            )
            return "generatePlanSummary"

        if has_enough_info:
            logger.info(
                "[Graph Router] Has enough info. Routing to generatePlanSummary."
            )
            return "generatePlanSummary"
        else:
            # Check if there's a transition message that was created but needs to be sent
            messages = state.get("messages", [])
            if messages:
                last_message = messages[-1]
                if hasattr(last_message, "content") and last_message.content:
                    content_lower = last_message.content.lower()
                    # Check if the last message looks like a transition message
                    transition_indicators = [
                        "are you ready for me to generate",
                        "ready for me to generate your detailed",
                        "ready to proceed",
                        "would you like to share any additional information",
                        "let me know when you're ready",
                    ]

                    if any(
                        indicator in content_lower
                        for indicator in transition_indicators
                    ):
                        logger.info(
                            "[Graph Router] 🎯 Transition message detected but checkCompletion said not enough info. "
                            "The transition message should still be sent to user for their decision."
                        )
                        # The message is already in the state, so ending here will send it to the frontend
                        logger.info(
                            "[Graph Router] Ending turn to send transition message and await user response."
                        )
                        return "END"

            logger.info(
                "[Graph Router] Need more info. Ending turn to await user response."
            )
            return "END"

    async def _plan_summary_confirmation_router(
        self, state: OnboardingState
    ) -> Literal["generatePlan", "gatherInfo", "END"]:
        """Router for plan summary confirmation - uses LLM to determine user intent"""
        messages = state.get("messages", [])
        plan_summary_sent = state.get("plan_summary_sent", False)
        onboarding_stage = state.get("onboarding_stage", "")

        logger.info(
            f"[Graph Router] DEBUG - plan_summary_sent: {plan_summary_sent}, onboarding_stage: {onboarding_stage}, messages: {len(messages)}"
        )

        # Log the last few messages to understand the conversation flow
        if messages:
            logger.info(f"[Graph Router] Last 3 messages in conversation:")
            for i, msg in enumerate(messages[-3:]):
                msg_type = msg.type if hasattr(msg, "type") else "unknown"
                msg_content = (
                    msg.content[:100] if hasattr(msg, "content") else str(msg)[:100]
                )
                logger.info(
                    f"  Message {i}: type={msg_type}, content='{msg_content}...'"
                )

        # ENHANCED: Check if the last AI message looks like a plan summary (fallback detection)
        last_ai_message_is_plan_summary = False
        if messages:
            for msg in reversed(messages):
                if hasattr(msg, "type") and msg.type == "ai":
                    content = msg.content.lower()
                    plan_summary_indicators = [
                        "are you ready for me to generate",
                        "ready for me to generate your detailed",
                        "training plan?",
                        "plan summary",
                        "approach:",
                        "weekly structure:",
                        "key focus:",
                        "progression:",
                        "phases:",
                    ]
                    if any(
                        indicator in content for indicator in plan_summary_indicators
                    ):
                        last_ai_message_is_plan_summary = True
                        logger.info(
                            f"[Graph Router] 🎯 FALLBACK: Detected plan summary in last AI message: '{content[:100]}...'"
                        )
                    break

        # ADDITIONAL FALLBACK: Check sidebar data for plan summary stage
        sidebar_stage_is_plan_summary = False
        sidebar_data = state.get("sidebar_data", {})
        if isinstance(sidebar_data, dict):
            current_stage = sidebar_data.get("current_stage", "")
            if current_stage == "plan_summary_ready":
                sidebar_stage_is_plan_summary = True
                logger.info(
                    f"[Graph Router] 🎯 SIDEBAR FALLBACK: Detected plan_summary_ready in sidebar: {current_stage}"
                )
        elif hasattr(sidebar_data, "current_stage"):
            if sidebar_data.current_stage == "plan_summary_ready":
                sidebar_stage_is_plan_summary = True
                logger.info(
                    f"[Graph Router] 🎯 SIDEBAR FALLBACK: Detected plan_summary_ready in sidebar: {sidebar_data.current_stage}"
                )

        logger.info(
            f"[Graph Router] ENHANCED DEBUG - plan_summary_sent: {plan_summary_sent}, "
            f"onboarding_stage: {onboarding_stage}, "
            f"last_ai_message_is_plan_summary: {last_ai_message_is_plan_summary}, "
            f"sidebar_stage_is_plan_summary: {sidebar_stage_is_plan_summary}, "
            f"messages: {len(messages)}"
        )

        # ENHANCED LOGIC: Check if user is returning after plan summary was sent
        # This handles the case where user returns to platform after plan summary
        # ADDED FALLBACKS: Also detect based on message content or sidebar data if state flags are lost
        if (
            plan_summary_sent
            or onboarding_stage == "plan_summary_ready"
            or last_ai_message_is_plan_summary
            or sidebar_stage_is_plan_summary
        ):
            logger.info(
                "[Graph Router] User is at plan summary stage, checking for user response..."
            )

            # RECOVERY: If we detected a plan summary by content or sidebar but flags weren't set, update them
            if (
                last_ai_message_is_plan_summary or sidebar_stage_is_plan_summary
            ) and not (plan_summary_sent or onboarding_stage == "plan_summary_ready"):
                logger.info(
                    "[Graph Router] 🔧 RECOVERY: Updating state flags based on fallback detection"
                )
                state["plan_summary_sent"] = True
                state["onboarding_stage"] = "plan_summary_ready"

            # Look for the most recent human message AFTER the plan summary
            last_human_message = None
            last_ai_message = None
            plan_summary_message_found = False

            # Find the last AI message (should be the plan summary) and last human message
            # We need to ensure the human message comes AFTER the plan summary
            for msg in reversed(messages):
                if hasattr(msg, "type"):
                    if msg.type == "ai" and last_ai_message is None:
                        last_ai_message = msg
                        # Check if this AI message is the plan summary
                        if msg.content and any(
                            indicator in msg.content.lower()
                            for indicator in [
                                "are you ready for me to generate",
                                "ready for me to generate your detailed",
                                "training plan?",
                                "plan summary",
                                "approach:",
                                "weekly structure:",
                                "key focus:",
                                "progression:",
                                "phases:",
                            ]
                        ):
                            plan_summary_message_found = True
                    elif (
                        msg.type == "human"
                        and last_human_message is None
                        and plan_summary_message_found
                    ):
                        # Only consider human messages that come AFTER we found the plan summary
                        last_human_message = msg
                        break

            # Check if the last AI message looks like a plan summary
            is_plan_summary = False
            if last_ai_message and last_ai_message.content:
                content = last_ai_message.content.lower()
                plan_summary_keywords = [
                    "plan",
                    "ready",
                    "generate",
                    "training",
                    "approach",
                    "phases",
                    "weekly",
                ]
                is_plan_summary = any(
                    keyword in content for keyword in plan_summary_keywords
                )

            logger.info(
                f"[Graph Router] Last AI message is plan summary: {is_plan_summary}"
            )
            logger.info(
                f"[Graph Router] Last human message exists: {last_human_message is not None}"
            )
            logger.info(
                f"[Graph Router] Plan summary message found: {plan_summary_message_found}"
            )

            # FIXED: If we have a human message AFTER the plan summary, analyze it as a response
            if last_human_message and last_human_message.content.strip():
                user_response = last_human_message.content.strip()
                logger.info(
                    f"[Graph Router] User responded to plan summary with: '{user_response}'"
                )
                # Reset the flag since we're processing the response
                state["plan_summary_sent"] = False
                # Continue to analyze the response below
            else:
                # No human message found after plan summary - wait for user response
                logger.info(
                    "[Graph Router] 🎯 No user response to plan summary yet - ending to wait for user input"
                )
                return "END"
        else:
            # No plan summary context, look for any human message
            if not messages:
                logger.info("[Graph Router] No messages found, ending.")
                return "END"

            # Find the last human message
            last_human_message = None
            for msg in reversed(messages):
                if hasattr(msg, "type") and msg.type == "human":
                    last_human_message = msg
                    break

            if not last_human_message:
                logger.info("[Graph Router] No human message found, ending.")
                return "END"

            user_response = last_human_message.content.strip()
            logger.info(f"[Graph Router] Analyzing user response: '{user_response}'")

        # Use LLM to determine user intent instead of keyword matching
        # CRITICAL: This LLM call is for INTERNAL ROUTING ONLY - never send to frontend
        try:
            from athlea_langgraph.services.azure_openai_service import (
                create_azure_chat_openai,
            )
            from langchain_core.messages import SystemMessage, HumanMessage

            # Create a separate LLM instance for routing (not part of message chain)
            router_llm = create_azure_chat_openai(temperature=0.1).with_config(
                {"tags": ["internal", "router", "no_stream"]}
            )

            routing_prompt = """You are a routing assistant for a fitness onboarding system. The user was just presented with a plan summary and asked for confirmation.

Based on their response, determine their intent:

1. "APPROVE" - They want to proceed with generating the full plan. This includes:
   - Any form of agreement, approval, or desire to continue
   - Phrases like "looks good", "let's proceed", "yes", "okay", "sounds great"
   - Requests to see the full plan like "let me see the plan", "show me the plan", "generate the plan"
   - Any positive acknowledgment that indicates readiness to move forward

2. "ADD_INFO" - They want to add more information or clarify something before the plan is generated:
   - Explicit requests to change or add information
   - Questions about the plan summary that require clarification
   - Statements indicating they want to modify their goals or preferences
   - IMPORTANT: If the user is starting a NEW conversation (greetings like "hi", "hello", "how can you help") this is ADD_INFO

3. "UNCLEAR" - Their response is unclear or ambiguous

User's response: "{user_response}"

Important: 
- If the user says anything positive about the plan summary or asks to see the full plan, choose APPROVE.
- If the user appears to be starting a fresh conversation with a greeting, choose ADD_INFO to gather their information.

Respond with ONLY one word: APPROVE, ADD_INFO, or UNCLEAR"""

            # INTERNAL ROUTING CALL - This response should NEVER reach the frontend
            router_response = await router_llm.ainvoke(
                [
                    SystemMessage(
                        content=routing_prompt.format(user_response=user_response)
                    )
                ]
            )

            # Extract intent for internal routing decision only
            intent = router_response.content.strip().upper()
            logger.info(
                f"[Graph Router] INTERNAL LLM routing decision: {intent} (not for frontend)"
            )

            # FALLBACK: Check for common approval phrases if LLM fails
            approval_phrases = [
                "looks good",
                "let me see",
                "show me the plan",
                "generate the plan",
                "sounds good",
                "perfect",
                "great",
                "yes",
                "okay",
                "ok",
                "proceed",
                "let's go",
                "continue",
                "ready",
            ]

            user_lower = user_response.lower()
            has_approval_phrase = any(
                phrase in user_lower for phrase in approval_phrases
            )

            logger.info(
                f"[Graph Router] Fallback check - has approval phrase: {has_approval_phrase}"
            )

            if intent == "APPROVE" or (
                intent not in ["APPROVE", "ADD_INFO"] and has_approval_phrase
            ):
                if intent not in ["APPROVE", "ADD_INFO"]:
                    logger.info(
                        f"[Graph Router] 🔧 FALLBACK: LLM returned '{intent}', but approval phrase detected - overriding to APPROVE"
                    )
                logger.info(
                    "[Graph Router] ✅ User approved plan summary. Proceeding to generatePlan."
                )
                return "generatePlan"
            elif intent == "ADD_INFO":
                logger.info(
                    "[Graph Router] 🔄 User wants to add more info. Routing back to gatherInfo."
                )
                return "gatherInfo"
            else:
                logger.info(
                    "[Graph Router] ❓ Unclear response to plan summary. Ending for clarification."
                )
                return "END"

        except Exception as e:
            logger.error(
                f"[Graph Router] Error in LLM routing: {e}. Using fallback logic."
            )

            # EMERGENCY FALLBACK: Simple keyword matching
            approval_keywords = [
                "good",
                "yes",
                "ok",
                "okay",
                "proceed",
                "plan",
                "see",
                "show",
                "generate",
            ]
            user_lower = user_response.lower()

            if any(keyword in user_lower for keyword in approval_keywords):
                logger.info(
                    "[Graph Router] 🔧 EMERGENCY FALLBACK: Detected approval keywords, proceeding to generatePlan"
                )
                return "generatePlan"
            else:
                logger.info(
                    "[Graph Router] 🔧 EMERGENCY FALLBACK: No approval detected, ending"
                )
                return "END"

    async def _onboarding_plan_feedback_router(
        self, state: OnboardingState
    ) -> Literal["sessionGeneration", "generatePlan", "END"]:
        """Router specifically for onboarding plan feedback - handles approval vs adjustment requests"""
        messages = state.get("messages", [])
        if not messages:
            logger.info("[Graph Router] No messages found, ending.")
            return "END"

        # Find the last human message
        last_human_message = None
        for msg in reversed(messages):
            if hasattr(msg, "type") and msg.type == "human":
                last_human_message = msg
                break

        if not last_human_message:
            logger.info("[Graph Router] No human message found, ending.")
            return "END"

        user_response = last_human_message.content.strip()
        logger.info(
            f"[Graph Router] Analyzing onboarding plan feedback: '{user_response}'"
        )

        # Use LLM to determine user intent
        # CRITICAL: This LLM call is for INTERNAL ROUTING ONLY - never send to frontend
        try:
            from athlea_langgraph.services.azure_openai_service import (
                create_azure_chat_openai,
            )
            from langchain_core.messages import SystemMessage

            # Create a separate LLM instance for routing (not part of message chain)
            router_llm = create_azure_chat_openai(temperature=0.1).with_config(
                {"tags": ["internal", "router", "no_stream"]}
            )

            routing_prompt = """You are a routing assistant for a fitness coaching system. The user was just presented with their complete training plan and asked for feedback.

Based on their response, determine their intent:

1. "APPROVE" - They approve the plan and want to proceed to session generation (any form of approval, satisfaction, or readiness to start)
2. "MODIFY" - They want changes, modifications, or have feedback about the plan
3. "UNCLEAR" - Their response is unclear or ambiguous

User's response: "{user_response}"

Respond with ONLY one word: APPROVE, MODIFY, or UNCLEAR"""

            # INTERNAL ROUTING CALL - This response should NEVER reach the frontend
            router_response = await router_llm.ainvoke(
                [
                    SystemMessage(
                        content=routing_prompt.format(user_response=user_response)
                    )
                ]
            )

            # Extract intent for internal routing decision only
            intent = router_response.content.strip().upper()
            logger.info(
                f"[Graph Router] INTERNAL LLM routing decision: {intent} (not for frontend)"
            )

            # Check for common approval phrases as fallback
            approval_phrases = [
                "looks good",
                "perfect",
                "great",
                "love it",
                "like it",
                "this works",
                "sounds good",
                "excellent",
                "awesome",
                "fantastic",
                "yes",
                "okay",
                "ok",
            ]

            adjustment_phrases = [
                "change",
                "adjust",
                "modify",
                "different",
                "more",
                "less",
                "harder",
                "easier",
                "intense",
                "days",
                "focus",
                "add",
                "remove",
                "instead",
            ]

            user_lower = user_response.lower()
            has_approval_phrase = any(
                phrase in user_lower for phrase in approval_phrases
            )
            has_adjustment_phrase = any(
                phrase in user_lower for phrase in adjustment_phrases
            )

            if intent == "APPROVE" or (
                intent not in ["APPROVE", "MODIFY"]
                and has_approval_phrase
                and not has_adjustment_phrase
            ):
                logger.info(
                    "[Graph Router] ✅ User approved the onboarding plan. Proceeding to session generation."
                )
                return "sessionGeneration"  # Proceed to session generation
            elif intent == "MODIFY" or has_adjustment_phrase:
                logger.info(
                    "[Graph Router] 🔄 User wants plan adjustments. Regenerating plan with feedback."
                )
                return "generatePlan"  # Regenerate plan with feedback
            else:
                logger.info(
                    "[Graph Router] ❓ Unclear response to onboarding plan. Ending for clarification."
                )
                return "END"

        except Exception as e:
            logger.error(
                f"[Graph Router] Error in onboarding plan feedback routing: {e}. Defaulting to end."
            )
            return "END"

    def compile(self) -> StateGraph:
        """Compile the graph with checkpointer"""
        if self._graph is None:
            graph = self.create_graph()
            self._graph = graph.compile(
                checkpointer=self.checkpointer,
                interrupt_after=[
                    # Removed "generatePlanSummary" - confirmation handled by router
                    "plan_review",
                    "userControl",
                ],
            )
            logger.info("[OnboardingGraph] Enhanced graph compiled successfully")

        return self._graph

    async def ainvoke(
        self, input_data: Dict[str, Any], config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Async invoke the graph"""
        graph = self.compile()
        return await graph.ainvoke(input_data, config)

    def invoke(
        self, input_data: Dict[str, Any], config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Sync invoke the graph"""
        graph = self.compile()
        return graph.invoke(input_data, config)

    async def astream(self, input_data: Dict[str, Any], config: Dict[str, Any] = None):
        """Async stream the graph execution"""
        graph = self.compile()
        async for chunk in graph.astream(input_data, config):
            yield chunk

    def stream(self, input_data: Dict[str, Any], config: Dict[str, Any] = None):
        """Sync stream the graph execution"""
        graph = self.compile()
        for chunk in graph.stream(input_data, config):
            yield chunk


def create_onboarding_graph(config=None):
    """Factory function to create an enhanced onboarding graph for LangGraph server

    Args:
        config: RunnableConfig object containing configuration

    Returns:
        StateGraph: Compiled graph ready for execution
    """
    logger.info(
        "[create_onboarding_graph] Creating new enhanced onboarding graph instance for LangGraph server"
    )

    # Create the onboarding graph instance
    onboarding_graph = OnboardingGraph(
        checkpointer=None,  # LangGraph server handles checkpointing
        enable_mem0=True,
        mem0_use_api=False,
    )

    # Return the compiled graph
    return onboarding_graph.compile()


def get_compiled_onboarding_graph(
    checkpointer=None,
    enable_mem0: bool = True,
    mem0_use_api: bool = False,
) -> StateGraph:
    """Get a compiled enhanced onboarding graph ready for use

    Args:
        checkpointer: Optional checkpointer for state persistence
        enable_mem0: Whether to enable Mem0 memory integration
        mem0_use_api: Whether to use Mem0 API (True) or local instance (False)

    Returns:
        StateGraph: Compiled graph ready for execution
    """
    logger.info(
        "[get_compiled_onboarding_graph] Getting compiled enhanced onboarding graph"
    )
    # Corrected: this function was calling create_onboarding_graph which is for the server
    # and doesn't pass the checkpointer. It should instantiate the class directly.
    onboarding_graph = OnboardingGraph(
        checkpointer=checkpointer,
        enable_mem0=enable_mem0,
        mem0_use_api=mem0_use_api,
    )
    return onboarding_graph.compile()


# For backward compatibility and easy imports
def create_initial_state(user_id: str) -> OnboardingState:
    """Create initial onboarding state for a user"""
    return create_initial_onboarding_state(user_id)
